import type { RouteRecordRawExt } from '@/router'
import { defineStore } from 'pinia'
import { asyncRoutes } from '@/router'
import { store } from '@/store/index'
import { useUserStore } from '@/store/user'

export const usePermissionStore = defineStore('permission', {
  state: () => ({
    routes: [],
    removeRoutesFn: [],
  }),
  actions: {
    generateRoutes(permIds: string[]) {
      /** 将权限id列表转成map格式，方便后续判断时提高性能 */
      const permMap = {}
      for (const id of permIds) {
        permMap[id] = true
      }

      const routes = filterAsyncRoutes(asyncRoutes, permMap)

      this.routes = routes
      return routes
    },
    /**
     * 清空动态路由
     */
    clearRoutes() {
      this.routes = []
      for (const fn of this.removeRoutesFn) {
        fn()
      }
    },
  },
})

// 非setup
export function usePermissionStoreHook() {
  return usePermissionStore(store)
}

/**
 * 传入路由和权限map，递归遍历路由数组，返回有权限的路由
 * @param routes 需要过滤的路由
 * @param permMap 权限ID映射对象
 * @returns 过滤后的路由
 */
function filterAsyncRoutes(routes: RouteRecordRawExt[], permMap: Record<number, boolean>): RouteRecordRawExt[] {
  const res = []

  for (const route of routes) {
    const tmp = { ...route }

    // 如果路由没有meta或者没有permission属性，或者permission存在于permMap中
    if (!tmp.meta || !tmp.meta.permission || permMap[tmp.meta.permission]) {
      // 如果存在子路由，递归过滤子路由
      if (tmp.children?.length) {
        tmp.children = filterAsyncRoutes(tmp.children, permMap)
        // 如果子路由过滤后还有子路由，将该路由添加到结果数组中
        if (tmp.children.length) {
          res.push(tmp)
        }
      }
      else {
        res.push(tmp)
      }
    }
  }

  return res
}

/**
 * 验证按钮权限
 */
export function auth(value: string | string[]) {
  const userStore = useUserStore()
  const { roles } = userStore.user
  const perMap = userStore.permMap

  // 超级管理员 拥有所有权限
  if (roles.includes('ROOT')) {
    return true
  }

  return typeof value === 'string' ? perMap[value] : value.some(perm => perMap[perm])
}
