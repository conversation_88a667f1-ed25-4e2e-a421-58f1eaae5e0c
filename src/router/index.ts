import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHashHistory } from 'vue-router'
import Layout from '@/layout/Layout.vue'
import MultilevelLayout from '@/views/MultilevelLayout.vue'

export type RouteRecordRawExt = RouteRecordRaw & { hidden?: boolean, children?: RouteRecordRawExt[] }

export const constantRoutes: Array<RouteRecordRawExt> = [
  {
    path: '/redirect',
    // component: Layout,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/Redirect.vue'),
      },
    ],
  },
  {
    path: '/404',
    component: () => import('@/views/404.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/404.vue'),
  },
  {
    path: '/login',
    component: () => import('@/views/login/Login.vue'),
  },
]

export const sysManageRoutes: Array<RouteRecordRawExt> = [

  {
    path: '/dashboard',
    component: Layout,
    meta: { title: '仪表盘', icon: 'dashboard' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/template/Home.vue'),
        meta: { title: '主控台' },
      },
      {
        path: 'table',
        component: () => import('@/views/template/Table.vue'),
        meta: { title: '基础表格', icon: 'list' },
      },
      {
        path: 'form',
        component: () => import('@/views/template/Form.vue'),
        meta: { title: '基础表单', icon: 'form' },
      },
      {
        path: 'addressSelect',
        component: () => import('@/views/template/SelectAddress.vue'),
        meta: { title: '选择地址' },
      },

    ],
  },
  {
    path: '/home',
    component: Layout,
    meta: { title: '单菜单', icon: 'list', singleChild: true },
    children: [
      {
        path: 'index',
        component: () => import('@/views/Empty.vue'),
      },
    ],
  },

  {
    path: '/form',
    component: Layout,
    meta: { title: '表单页面', icon: 'form' },
    children: [
      {
        path: 'base',
        component: () => import('@/views/template/Form.vue'),
        meta: { title: '基础表单' },
      },
      {
        path: 'supper',
        component: () => import('@/views/template/SupperForm.vue'),
        meta: { title: '高级表单' },
      },
      {
        path: 'supper2',
        component: () => import('@/views/template/SupperForm2.vue'),
        meta: { title: '高级表单-检验' },
      },

    ],
  },
  {
    path: '/list',
    component: Layout,
    meta: { title: '列表页', icon: 'list' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/template/List.vue'),
        meta: { title: '列表页' },
      },
      {
        path: 'card',
        component: () => import('@/views/template/ListCard.vue'),
        meta: { title: '列表页-卡片式' },
      },
      {
        path: 'monitor',
        component: () => import('@/views/template/Monitor.vue'),
        meta: { title: '监控' },
      },

      // {
      //   path: 'card2',
      //   component: () => import('@/views/template/ListCard2.vue'),
      //   meta: { title: '列表页-卡片式2' },
      // },

    ],
  },

  {
    path: '/detail',
    component: Layout,
    meta: { title: '详情页', icon: 'detail' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/Empty.vue'),
        meta: { title: '主控台' },
      },
    ],
  },
  {
    path: '/result',
    component: Layout,
    meta: { title: '结果页面', icon: 'result' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/Empty.vue'),
        meta: { title: '主控台' },
      },
    ],
  },
  {
    path: '/error',
    component: Layout,
    meta: { title: '异常页面', icon: 'error' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/Empty.vue'),
        meta: { title: '主控台' },
      },
    ],
  },
  {
    path: '/setting',
    component: Layout,
    meta: { title: '设置页面', icon: 'setting' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/Empty.vue'),
        meta: { title: '主控台' },
      },
    ],
  },
  {
    path: '/system',
    component: Layout,
    meta: { title: '系统管理', icon: 'file' },
    children: [

      {
        path: 'user',
        meta: { title: '用户管理', icon: 'home' },
        component: () => import('@/views/system/user/User.vue'),
      },

      {
        path: 'role',
        meta: { title: '角色管理', icon: 'home' },
        component: () => import('@/views/system/role/Role.vue'),
      },

      {
        path: 'third',
        meta: { title: '三级目录测试', icon: 'home' },
        component: MultilevelLayout,
        children: [
          {
            path: 'user',
            meta: { title: '用户管理', icon: 'home' },
            component: () => import('@/views/system/user/User.vue'),
          },
        ],
      },

      /**
       *  菜单管理仅在开发环境显示，通过在生产环境给一个无效的权限码实现这个功能
       */
      {
        path: 'menu',
        component: () => import('@/views/system/menu/Menu.vue'),
        meta: {
          title: '菜单管理',
          icon: 'home',
          permission: import.meta.env.DEV ? null : '__VALUE__',
        },
      },

    ],
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  // 重置原生滚动条位置
  scrollBehavior: (to, form, savedPosition) => {
    if (savedPosition) {
      return savedPosition
    }

    return { top: 0 }
  },

})

export const asyncRoutes: Array<RouteRecordRawExt> = [...sysManageRoutes]

/**
 * 获取第一级路由path
 */
export function getFirstRoutePath(routes: RouteRecordRaw[]) {
  if (!routes.length)
    return ''

  const firstRoute = routes[0]
  let path = firstRoute.path

  if (firstRoute.children?.length) {
    const childPath = getFirstRoutePath(firstRoute.children)
    path = childPath.startsWith('/') ? childPath : `${path}/${childPath}`
  }

  return path
}

export default router
